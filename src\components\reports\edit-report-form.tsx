"use client";

import { useState, useEffect } from "react";
import { useActionState } from "react";
import { useFormStatus } from "react-dom";
import { MeetingReport } from "@/lib/types";
import { updateReport } from "@/actions/reports/update-report";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { IconSpinner } from "@/components/ui/icons";
import { toast } from "sonner";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

type UpdateReportState =
  | {
      success: false;
      error: string;
      issues?: undefined;
      updatedSlug?: undefined;
    }
  | {
      success: false;
      error: string;
      issues: {
        title?: string[];
        content?: string[];
        reportDate?: string[];
        reportType?: string[];
        visibility?: string[];
        published?: string[];
        workspaceId?: string[];
        reportId?: string[];
      };
      updatedSlug?: undefined;
    }
  | { success: true; error: ""; issues?: undefined; updatedSlug?: string };

interface EditReportFormProps {
  report: MeetingReport;
}

export function EditReportForm({ report }: EditReportFormProps) {
  const [htmlContent, setHtmlContent] = useState(report.content);

  const initialState: UpdateReportState = { success: false, error: "" };
  const [state, dispatch] = useActionState(updateReport, initialState);

  // Handle toast notifications
  useEffect(() => {
    if (state.success) {
      toast.success("Report updated successfully!");
    } else if (state.error && state.error !== "") {
      toast.error(state.error);
    }
  }, [state]);

  const formatDateForInput = (date: Date) => {
    const d = new Date(date);
    return d.toISOString().split("T")[0]; // Simpler and safer
  };

  function SubmitButton() {
    const { pending } = useFormStatus();

    return (
      <div className="flex justify-end space-x-4">
        <Button type="submit" disabled={pending}>
          {pending ? (
            <>
              <IconSpinner className="mr-2 h-4 w-4 animate-spin" />
              Updating...
            </>
          ) : (
            "Update Report"
          )}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            (window.location.href = `/workspaces/${report.workspaceId}/reports/${report.slug}`)
          }
        >
          {state.success ? "View Report" : "Cancel"}
        </Button>
      </div>
    );
  }

  return (
    <form action={dispatch} className="space-y-6 max-w-4xl">
      <input type="hidden" name="workspaceId" value={report.workspaceId} />
      <input type="hidden" name="reportId" value={report.id} />
      <input type="hidden" name="content" value={htmlContent} />

      <div className="space-y-2">
        <Label htmlFor="title" className="font-bold">
          Report Title
        </Label>
        <Input
          id="title"
          name="title"
          defaultValue={report.title}
          required
          className="bg-muted hover:cursor-pointer"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="reportDate" className="font-bold">
            Report Date
          </Label>
          <Input
            id="reportDate"
            name="reportDate"
            type="date"
            defaultValue={formatDateForInput(report.reportDate)}
            required
            className="bg-muted hover:cursor-pointer"
          />
          {state.issues?.reportDate && (
            <p className="text-sm text-red-500">{state.issues.reportDate[0]}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="reportType" className="font-bold">
            Report Type
          </Label>
          <Select name="reportType" defaultValue={report.reportType}>
            <SelectTrigger className="bg-muted hover:cursor-pointer">
              <SelectValue placeholder="Select a type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="MONTHLY">Monthly</SelectItem>
              <SelectItem value="ANNUAL">Annual</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        <div className="space-y-2">
          <Label htmlFor="visibility" className="font-bold">
            Visibility
          </Label>
          <Select name="visibility" defaultValue={report.visibility}>
            <SelectTrigger className="bg-muted hover:cursor-pointer">
              <SelectValue placeholder="Select visibility" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="WORKSPACE_MEMBERS">
                Workspace Members Only
              </SelectItem>
              <SelectItem value="PUBLIC">Public</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2 mx-auto my-auto">
            <Switch
              id="published"
              name="published"
              defaultChecked={report.published}
            />
            <Label htmlFor="published" className="font-bold cursor-pointer">
              Published
            </Label>
          </div>
          <p className="text-sm text-muted-foreground -mt-4">
            If unchecked, the report will be saved as a draft and will not be
            visible to anyone except admins.
          </p>
        </div>
      </div>

      {state.issues?.title && (
        <p className="text-sm text-red-500">{state.issues.title[0]}</p>
      )}

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label htmlFor="content" className="font-bold">
            Report Content
          </Label>
          <div className="flex gap-2"></div>
        </div>

        <Textarea
          value={htmlContent}
          onChange={(e) => setHtmlContent(e.target.value)}
          rows={20}
          className="font-mono text-sm bg-muted"
          placeholder="Edit raw HTML content..."
        />

        {state.issues?.content && (
          <p className="text-sm text-red-500">{state.issues.content[0]}</p>
        )}
      </div>
      <div className="flex flex-row justify-center items-center space-x-2">
        <SubmitButton />
      </div>
    </form>
  );
}
