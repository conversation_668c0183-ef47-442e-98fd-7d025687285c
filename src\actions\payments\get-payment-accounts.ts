"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { $Enums } from "@/lib/types";

export async function getPaymentAccounts(query?: string): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    if (!query) {
      return { success: true, data: [] };
    }

    const accounts = await prisma.paymentAccount.findMany({
      where: {
        accountName: {
          contains: query,
          mode: 'insensitive',
        },
        accountType: {
          in: [$Enums.PaymentAccountType.PAYEE, $Enums.PaymentAccountType.BOTH],
        },
        isActive: true,
      },
      orderBy: {
        accountName: "asc",
      },
    });

    return { success: true, data: accounts };
  } catch (error) {
    console.error("Error fetching payment accounts:", error);
    return {
      success: false,
      error: "Failed to fetch payment accounts",
    };
  }
}
