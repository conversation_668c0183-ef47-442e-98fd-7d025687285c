"use client";

import React, { useState, useMemo, useRef, useEffect } from "react";
import {
  Copy,
  Download,
  Plus,
  Trash2,
  Edit2,
  Save,
  X,
  Printer,
  Loader2,
  Banknote,
  Square,
  SquareCheckBig,
} from "lucide-react";
import {
  usePaymentRequests,
  useCreatePaymentRequest,
  useCreatePayment,
  useUpdatePayment,
  useDeletePayment,
  useToggleAccountInfo,
  useToggleAccountInfoAll,
  useCreateBankAccount,
  useUpdateBankAccount,
  useDeleteBankAccount,
  PaymentAccountType,
  type PaymentRequest,
  type Payment,
  type PaymentAccount,
} from "@/hooks/use-payment-requests";
import { Combobox } from "@/components/payment/combobox";
import { useReactToPrint } from "react-to-print";
import { getPaymentAccounts } from "@/actions/payments/get-payment-accounts";
import { generatePaymentRequestForm } from "@/components/payment/generate-payment-request-form";
import { generateRemittanceSlip } from "@/components/payment/generate-remittance-slip";
import { DeleteConfirmationModal } from "@/components/payment/delete-confirmation-modal";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

const PAYEE_OPTIONS = [
  { value: "汽車專戶7777-7", label: "汽車專戶7777-7" },
  { value: "東聯保全", label: "東聯保全" },
  { value: "明陽機電", label: "明陽機電" },
  { value: "日立永大電機", label: "日立永大電機" },
  { value: "冠福管理維護", label: "冠福管理維護" },
  { value: "強迅安全系統", label: "強迅安全系統" },
  { value: "吳趙阿蝦", label: "吳趙阿蝦" },
];

const REMARKS_OPTIONS = [
  { value: "東聯保全服務", label: "東聯保全服務" },
  { value: "明陽車位年費", label: "明陽車位年費" },
  { value: "明陽車位許可證", label: "明陽車位許可證" },
  { value: "明陽車位維修", label: "明陽車位維修" },
  { value: "明陽機梯年費", label: "明陽機梯年費" },
  { value: "明陽機梯維修", label: "明陽機梯維修" },
  { value: "日立電梯保養", label: "日立電梯保養" },
  { value: "日立電梯許可證", label: "日立電梯許可證" },
  { value: "日立電梯維修", label: "日立電梯維修" },
  { value: "冠福機電保養", label: "冠福機電保養" },
  { value: "冠福機電維修", label: "冠福機電維修" },
  { value: "強迅監控維修", label: "強迅監控維修" },
  { value: "汽車專款", label: "汽車專款" },
  { value: "社區清潔費", label: "社區清潔費" },
];

const PaymentManager = () => {
  const [selectedPaymentRequest, setSelectedPaymentRequest] =
    useState<PaymentRequest | null>(null);
  // which row is being edited
  const [editingId, setEditingId] = useState<string | null>(null);
  // which field is being edited (null = whole row)
  const [isRowEditing, setIsRowEditing] = useState(false); // true = row edit mode
  const [editingField, setEditingField] = useState<string | null>(null);
  // form state
  const [editForm, setEditForm] = useState<Partial<Payment>>({});
  const [accountEditForm, setAccountEditForm] = useState<Partial<PaymentAccount>>({});
  const [showBankAccounts, setShowBankAccounts] = useState(false);
  const [editingAccountId, setEditingAccountId] = useState<string | null>(null);
  const [editingAccountField, setEditingAccountField] = useState<string | null>(null);
  const [isAccountRowEditing, setIsAccountRowEditing] = useState(false);
  const [isAddingNewAccount, setIsAddingNewAccount] = useState(false);
  const [selectedPayments, setSelectedPayments] = useState<Record<string, boolean>>({});

  const [bankAccountForm, setBankAccountForm] = useState<{
    accountName: string;
    accountNumber: string;
    bankCode: string;
    bankName: string;
    branchName: string;
    accountType: PaymentAccountType;
    isDefault: boolean;
    isActive: boolean;
  }>({
    accountName: "",
    accountNumber: "",
    bankCode: "",
    bankName: "",
    branchName: "",
    accountType: PaymentAccountType.PAYEE,
    isDefault: false,
    isActive: true,
  });

  const [paymentRequestForm, setPaymentRequestForm] = useState({
    name: "僑星福華社區",
    year: (new Date().getFullYear() - 1911).toString(),
    month: (new Date().getMonth() + 1).toString().padStart(2, "0"),
  });

  const [copyFromPrevious, setCopyFromPrevious] = useState(false);
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    paymentRequest: any | null;
  }>({ isOpen: false, paymentRequest: null });

  const handleSelectAll = (isChecked: boolean) => {
    if (!selectedPaymentRequest) return;
    const newSelectedPayments: Record<string, boolean> = {};
    if (isChecked) {
      for (const payment of selectedPaymentRequest.payments) {
        newSelectedPayments[payment.id] = true;
      }
    }
    setSelectedPayments(newSelectedPayments);
  };

  const handleSelectPayment = (paymentId: string, isChecked: boolean) => {
    setSelectedPayments(prev => ({
      ...prev,
      [paymentId]: isChecked,
    }));
  };

  const allPayments = selectedPaymentRequest?.payments || [];
  const selectedPaymentIds = Object.keys(selectedPayments).filter(id => selectedPayments[id]);
  const isAllSelected = allPayments.length > 0 && selectedPaymentIds.length === allPayments.length;

  const printablePayments = useMemo(() => {
    if (!selectedPaymentRequest) return [];
    
    const selectedIds = Object.keys(selectedPayments).filter(id => selectedPayments[id]);
    
    if (selectedIds.length === 0) {
      return selectedPaymentRequest.payments;
    }
    
    return selectedPaymentRequest.payments.filter(p => selectedPayments[p.id]);
  }, [selectedPaymentRequest, selectedPayments]);

  const isComboboxFocused = useRef(false);

  useEffect(() => {
    const findAndSetPayeeAccount = async () => {
      // Don't update if combobox is currently focused/open
      if (isComboboxFocused.current) {
        console.log("Skipping payee account update - combobox is active");
        return;
      }

      if (editingId && editForm.payee && editForm.payee.length > 1) {
        console.log("Finding payee account for:", editForm.payee);
        const result = await getPaymentAccounts(editForm.payee);
        if (result.success && result.data && result.data.length > 0) {
          const bestMatch = result.data.find((acc: PaymentAccount) =>
            acc.accountName.includes(editForm.payee!)
          );

          if (bestMatch) {
            setEditForm((currentForm) => {
              if (
                currentForm.id === editingId &&
                currentForm.payee === editForm.payee
              ) {
                return {
                  ...currentForm,
                  payeeAccountId: bestMatch.id,
                };
              }
              return currentForm;
            });
          }
        }
      }
    };

    const handler = setTimeout(() => {
      findAndSetPayeeAccount();
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [editForm.payee, editingId]);

  // Refs for different printing sections
  const summaryPrintRef = useRef<HTMLDivElement>(null); // For summary table
  const paymentRequestFormsPrintRef = useRef<HTMLDivElement>(null); // For individual payment request forms
  const remittanceSlipsPrintRef = useRef<HTMLDivElement>(null); // For remittance slips

  // React Query hooks
  const {
    data: paymentRequests = [],
    isLoading,
    error,
    refetch: refetchPaymentRequests,
  } = usePaymentRequests();

  const createPaymentRequestMutation = useCreatePaymentRequest();
  const createPaymentMutation = useCreatePayment();
  const updatePaymentMutation = useUpdatePayment();
  const deletePaymentMutation = useDeletePayment();
  const toggleAccountInfoMutation = useToggleAccountInfo();
  const toggleAccountInfoAllMutation = useToggleAccountInfoAll();

  const createPaymentAccountMutation = useCreateBankAccount();
  const updatePaymentAccountMutation = useUpdateBankAccount();
  const deletePaymentAccountMutation = useDeleteBankAccount();

  // Auto-select first payment request when data loads
  useEffect(() => {
    if (paymentRequests.length > 0) {
      if (selectedPaymentRequest) {
        const updatedSelected = paymentRequests.find(
          (pr) => pr.id === selectedPaymentRequest.id
        );
        setSelectedPaymentRequest(updatedSelected || paymentRequests[0]);
      } else {
        setSelectedPaymentRequest(paymentRequests[0]);
      }
    } else {
      setSelectedPaymentRequest(null);
    }
  }, [paymentRequests]);

  // Select all payments by default when a payment request is loaded
  useEffect(() => {
    if (selectedPaymentRequest) {
      const allSelected: Record<string, boolean> = {};
      selectedPaymentRequest.payments.forEach(payment => {
        allSelected[payment.id] = true;
      });
      setSelectedPayments(allSelected);
    } else {
      setSelectedPayments({}); // Clear selection if no request is selected
    }
  }, [selectedPaymentRequest]);

  const handleCreatePaymentRequest = async (shouldCopyFromPrevious = false) => {
    try {
      const requestData = {
        ...paymentRequestForm,
        selectedPaymentRequestId: selectedPaymentRequest?.id,
        copyFromPrevious: shouldCopyFromPrevious,
      };

      const result =
        await createPaymentRequestMutation.mutateAsync(requestData);
      if (result.success && result.data) {
        await refetchPaymentRequests();
        setSelectedPaymentRequest(result.data);
        setCopyFromPrevious(false); // Reset the checkbox
      } else {
        alert(result.error);
      }
    } catch (e) {
      alert("創建失敗，請檢查該月份是否已存在。");
    }
  };

  const handleAddPayment = async () => {
    if (!selectedPaymentRequest) {
      alert("請先選擇或創建請款單");
      return;
    }

    const existingIds = selectedPaymentRequest.payments.map((p) =>
      parseInt(p.sequenceId)
    );
    const nextId =
      existingIds.length > 0 ? Math.max(...existingIds) + 1 : 1140801;

    const newPayment = {
      sequenceId: nextId.toString(),
      accountingSubject: "",
      payee: "",
      remarks: "",
      month: `${selectedPaymentRequest.year}/${selectedPaymentRequest.month}`,
      amount: 0,
      paymentMethod: "匯款",
      showAccountInfo: false,
      paymentRequestId: selectedPaymentRequest.id,
    };

    const result = await createPaymentMutation.mutateAsync(newPayment);

    if (result.success && result.data) {
      setEditingId(result.data.id);
      setIsRowEditing(true);
      setEditForm(result.data);
    } else {
      alert(result.error);
    }
  };

  const handleDeletePayment = async (id: string) => {
    if (!confirm("確定要刪除此筆付款記錄嗎？")) return;
    await deletePaymentMutation.mutateAsync(id);
  };

  const handleSaveEdit = async () => {
    if (!editingId) return;

    const payment = selectedPaymentRequest?.payments.find(
      (p) => p.id === editingId
    );
    if (!payment) return;

    // ✅ Compare old vs new values
    const hasChanged = (Object.keys(editForm) as (keyof Payment)[]).some(
      (key) => editForm[key] !== payment[key]
    );

    if (!hasChanged) {
      // No changes → just exit edit mode
      setEditingId(null);
      setEditingField(null);
      setIsRowEditing(false);
      return;
    }

    try {
      const {
        id,
        createdAt,
        updatedAt,
        paymentRequestId,
        remitterAccount,
        payeeAccount,
        ...dataToUpdate
      } = editForm as any;
      await updatePaymentMutation.mutateAsync({
        id: editingId,
        data: dataToUpdate,
      });
    } catch (err) {
      console.error("Failed to update payment:", err);
    } finally {
      setEditingId(null);
      setEditForm({});
    }
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditForm({});
  };

  const handleToggleAccountInfoAll = async (
    paymentRequestId: string,
    showAccountInfoAll: boolean
  ) => {
    try {
      await toggleAccountInfoAllMutation.mutateAsync({
        paymentRequestId,
        show: !showAccountInfoAll, // toggle
      });
    } catch (err) {
      console.error("Failed to toggle account info:", err);
    }
  };

  const handleToggleAccountInfo = async (payment: Payment) => {
    await toggleAccountInfoMutation.mutateAsync({
      paymentId: payment.id,
      show: !payment.showAccountInfo,
    });
  };

  const exportToCSV = () => {
    if (!selectedPaymentRequest) return;
    const headers = [
      "序號",
      "會計科目",
      "受款人",
      "銀行備註",
      "月份",
      "金額",
      "付款方式",
      "受款人銀行代號",
      "受款人銀行",
      "受款人分行",
      "受款人帳號",
      "受款人戶名",
    ];
    const csvContent = [
      headers.join(","),
      ...selectedPaymentRequest.payments.map((p) =>
        [
          p.sequenceId,
          `"${p.accountingSubject}"`,
          `"${p.payee}"`,
          `"${p.remarks}"`,
          p.month,
          p.amount,
          `"${p.paymentMethod}"`,
          p.payeeAccount?.bankCode || "",
          `"${p.payeeAccount?.bankName || ""}"`,
          `"${p.payeeAccount?.branchName || ""}"`,
          p.payeeAccount?.accountNumber || "",
          `"${p.payeeAccount?.accountName || ""}"`,
        ].join(",")
      ),
    ].join("\n");
    const blob = new Blob(["\uFEFF" + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `${selectedPaymentRequest.name}_${selectedPaymentRequest.year}年${selectedPaymentRequest.month}月份請款明細表.csv`;
    link.click();
  };

  const printSummary = () => window.print();

  // Print functions using react-to-print
  const handlePrintSummary = useReactToPrint({
    contentRef: summaryPrintRef,
    documentTitle: `${selectedPaymentRequest?.name}_${selectedPaymentRequest?.year}年${selectedPaymentRequest?.month}月份請款明細表`,
    pageStyle: `
      @page {
        margin: 1cm;
        size: A4 portrait;
      }
      body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
    `,
  });

  // Row edit mode
  const handleRowEdit = (id: string, payment: Payment) => {
    setEditingId(id);
    setIsRowEditing(true);
    setEditForm(payment); // preload the entire row
  };

  // Cell edit mode
  const handleCellClick = (payment: Payment, field: string) => {
    console.log("handleCellClick called:", {
      paymentId: payment.id,
      field,
      currentEditingId: editingId,
      currentEditingField: editingField,
      isRowEditing,
    });
    if (editingId === payment.id && editingField === field) {
      return; // This prevents re-editing the same cell!
    }
    if (!isRowEditing) {
      // only allow if not in row edit mode
      setEditingId(payment.id);
      setEditingField(field);
      setIsRowEditing(false);

      // preload editForm if switching from view → edit
      setEditForm({
        ...payment,
      });
    }
  };

  const handlePrintPaymentRequestForms = useReactToPrint({
    contentRef: paymentRequestFormsPrintRef,
    documentTitle: `${selectedPaymentRequest?.name}_${selectedPaymentRequest?.year}年${selectedPaymentRequest?.month}月份請款單`,
    pageStyle: `
      @page { 
        margin: 0; 
        size: A4 portrait; 
      }
      body { 
        -webkit-print-color-adjust: exact; 
        print-color-adjust: exact; 
        margin: 0;
        padding: 0;
        font-family: serif;
      }
      .page-break { 
        page-break-after: always; 
      }
      .page-break:last-child {
        page-break-after: auto;
      }
      @media print {
        * {
          box-sizing: border-box;
        }
        .text-red-500, .text-red-600 {
          color: #dc2626 !important;
        }
        .border-red-500 {
          border-color: #dc2626 !important;
        }
        .bg-red-50 {
          background-color: #fef2f2 !important;
        }
      }
    `,
  });

  const handlePrintRemittanceSlips = useReactToPrint({
    contentRef: remittanceSlipsPrintRef,
    documentTitle: `${selectedPaymentRequest?.name}_${selectedPaymentRequest?.year}年${selectedPaymentRequest?.month}月份匯款憑證`,
    pageStyle: `
      @page {
        margin: 0.2cm;
        size: A4 portrait;
      }
      body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
      .page-break {
        page-break-after: always;
        break-after: page;
      }
      /* remove the global force that blocked breaks */
      * {
        page-break-inside: auto !important;
      }
    `,
  });

  const defaultRemitterAccount = useMemo(() => {
    return selectedPaymentRequest?.paymentAccounts.find(
      (acc) =>
        acc.isDefault &&
        (acc.accountType === PaymentAccountType.REMITTER ||
          acc.accountType === PaymentAccountType.BOTH)
    );
  }, [selectedPaymentRequest]);
 
  const resetBankAccountForm = () => {
    setEditingAccountId(null);
    setIsAddingNewAccount(false);
    setBankAccountForm({
      accountName: "",
      accountNumber: "",
      bankCode: "",
      bankName: "",
      branchName: "",
      accountType: PaymentAccountType.PAYEE,
      isDefault: false,
      isActive: true,
    });
  };

  const handleSaveAccountEdit = async () => {
    if (editingAccountId) {
      // This is an update to a shared account.
      await updatePaymentAccountMutation.mutateAsync({
        id: editingAccountId,
        data: accountEditForm,
      });
      resetBankAccountForm();
    } else {
      // This is creating a new account and linking it.
      if (!selectedPaymentRequest) return;
      const data = {
        accountData: bankAccountForm, // Use bankAccountForm for new accounts
        paymentRequestId: selectedPaymentRequest.id,
      };
      const response = await fetch("/api/payments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (response.ok) {
        refetchPaymentRequests();
        resetBankAccountForm();
      } else {
        const error = await response.json();
        alert(`Failed to save account: ${error.error}`);
      }
    }
  };

  const handleAccountRowEdit = (id: string, account: PaymentAccount) => {
    setEditingAccountId(id);
    setBankAccountForm(account);
  };

  const cancelAccountEdit = () => {
    setEditingAccountId(null);
    setIsAddingNewAccount(false);
    setEditingAccountField(null);
    setIsAccountRowEditing(false);
    setAccountEditForm({});
    setBankAccountForm({
      accountName: "",
      accountNumber: "",
      bankCode: "",
      bankName: "",
      branchName: "",
      accountType: PaymentAccountType.PAYEE,
      isDefault: false,
      isActive: true,
    });
  };

  const handleAccountCellClick = (account: PaymentAccount, field: string) => {
    if (editingAccountId === account.id && editingAccountField === field) {
      return; // This prevents re-editing the same cell!
    }
    if (!isAccountRowEditing) {
      // only allow if not in row edit mode
      setEditingAccountId(account.id);
      setEditingAccountField(field);
      setIsAccountRowEditing(false);

      // preload editForm if switching from view → edit
      setAccountEditForm({
        ...account,
      });
    }
  };

  const handleAddBankAccount = async () => {
    if (!selectedPaymentRequest) return;
    const newAccount = {
      accountName: "",
      accountNumber: "",
      bankCode: "",
      bankName: "",
      branchName: "",
      accountType: PaymentAccountType.PAYEE,
      isDefault: false,
      isActive: true,
    };
    setEditingAccountId(null);
    setIsAddingNewAccount(true);
    setBankAccountForm(newAccount);
  };

  const handleDeleteBankAccount = async (id: string) => {
    if (!selectedPaymentRequest) return;
    if (
      window.confirm(
        "Are you sure you want to unlink this account from this payment request?"
      )
    ) {
      const response = await fetch(
        `/api/payment-request-accounts/${id}?paymentRequestId=${selectedPaymentRequest.id}`,
        {
          method: "DELETE",
        }
      );
      if (response.ok) {
        refetchPaymentRequests();
      } else {
        alert("Failed to unlink account");
      }
    }
  };

  // Function to open delete modal
  const openDeleteModal = (paymentRequest: any) => {
    setDeleteModal({
      isOpen: true,
      paymentRequest: paymentRequest,
    });
  };

  // Function to close delete modal
  const closeDeleteModal = () => {
    setDeleteModal({
      isOpen: false,
      paymentRequest: null,
    });
  };

  // Function to handle successful deletion
  const handleDeleteSuccess = () => {
    // Clear selected payment request if it was the deleted one
    if (selectedPaymentRequest?.id === deleteModal.paymentRequest?.id) {
      setSelectedPaymentRequest(null);
    }
  };

  const totalAmount =
    selectedPaymentRequest?.payments.reduce((sum, p) => sum + p.amount, 0) || 0;
  const isLoadingMutation =
    createPaymentRequestMutation.isPending ||
    createPaymentMutation.isPending ||
    updatePaymentMutation.isPending ||
    deletePaymentMutation.isPending ||
    toggleAccountInfoMutation.isPending ||
    createPaymentAccountMutation.isPending ||
    updatePaymentAccountMutation.isPending ||
    deletePaymentAccountMutation.isPending;

  const remitterAccounts = useMemo(
    () =>
      selectedPaymentRequest?.paymentAccounts.filter(
        (acc) =>
          acc.isActive &&
          (acc.accountType === PaymentAccountType.REMITTER ||
            acc.accountType === PaymentAccountType.BOTH)
      ) || [],
    [selectedPaymentRequest]
  );
  /*const payeeAccounts = useMemo(
    () =>
      selectedPaymentRequest?.paymentAccounts.filter(
        (acc) =>
          acc.isActive &&
          (acc.accountType === PaymentAccountType.PAYEE ||
            acc.accountType === PaymentAccountType.BOTH)
      ) || [],
    [selectedPaymentRequest]
  );*/
  const payeeAccounts = useMemo(() => {
    const filtered =
      selectedPaymentRequest?.paymentAccounts.filter(
        (acc) =>
          acc.isActive &&
          (acc.accountType === PaymentAccountType.PAYEE ||
            acc.accountType === PaymentAccountType.BOTH)
      ) || [];

    // Append one "empty" option
    return [
      ...filtered,
      {
        id: "empty", // must be unique, not collide with real ids
        accountType: PaymentAccountType.PAYEE,
        isActive: false,
        accountName: "",   // or "未指定"
        accountNumber: "", // blank
        bankName: "",      // blank
        isPlaceholder: true, // 👈 mark so you can detect it in UI
      },
    ];
  }, [selectedPaymentRequest]);


  const showAccountInfoAll = useMemo(() => {
    if (!selectedPaymentRequest || !selectedPaymentRequest.payments)
      return false;

    const payments = selectedPaymentRequest.payments;

    if (payments.length === 0) return false;

    const allTrue = payments.every((p) => p.showAccountInfo);
    const allFalse = payments.every((p) => !p.showAccountInfo);

    // Case 1: All true → checked
    if (allTrue) return true;

    // Case 2: All false → unchecked
    if (allFalse) return false;

    // Case 3: Mixed → could return false (unchecked),
    //         or handle a "partial" state if you want (indeterminate).
    return false;
  }, [selectedPaymentRequest]);

  if (isLoading)
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="animate-spin" size={32} />
        <span className="ml-2">載入中...</span>
      </div>
    );
  if (error)
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">載入失敗: {error.message}</div>
      </div>
    );

  return (
    <div className="max-w-8xl mx-auto">
      <div className="mb-2 bg-white rounded-lg shadow-md p-6 print:hidden">
        <h1 className="text-2xl font-bold mb-4">社區請款管理系統</h1>
        <div className="flex flex-col justify-center items-center gap-2">
          <div>
            <label className="block text-sm font-medium mb-2">選擇請款單</label>
            <select
              value={selectedPaymentRequest?.id || ""}
              onChange={(e) => {
                const req =
                  paymentRequests.find((pr) => pr.id === e.target.value) ||
                  null;
                setSelectedPaymentRequest(req);
                if (req) {
                  setPaymentRequestForm({
                    name: req.name,
                    year: req.year,
                    month: req.month,
                  });
                }
              }}
              className="w-full border rounded px-3 py-2 mb-2"
            >
              <option value="">選擇現有請款單...</option>
              {paymentRequests.map((paymentRequest) => (
                <option key={paymentRequest.id} value={paymentRequest.id}>
                  {paymentRequest.name} - {paymentRequest.year}年
                  {paymentRequest.month}月
                </option>
              ))}
            </select>
            {/* Delete confirmation modal */}
            <DeleteConfirmationModal
              isOpen={deleteModal.isOpen}
              paymentRequest={deleteModal.paymentRequest}
              onClose={closeDeleteModal}
              onConfirm={handleDeleteSuccess}
            />
            <div className="flex flex-row justify-between items-center gap-4 mb-4 rounded-md">
              <div>
                <label className="block text-sm font-medium mb-2">
                  社區名稱
                </label>
                <input
                  type="text"
                  placeholder="社區名稱"
                  value={paymentRequestForm.name}
                  onChange={(e) =>
                    setPaymentRequestForm({
                      ...paymentRequestForm,
                      name: e.target.value,
                    })
                  }
                  className="border rounded px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">年度</label>
                <input
                  type="text"
                  placeholder="年度"
                  value={paymentRequestForm.year}
                  onChange={(e) =>
                    setPaymentRequestForm({
                      ...paymentRequestForm,
                      year: e.target.value,
                    })
                  }
                  className="border rounded px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">月份</label>
                <input
                  type="text"
                  placeholder="月份"
                  value={paymentRequestForm.month}
                  onChange={(e) =>
                    setPaymentRequestForm({
                      ...paymentRequestForm,
                      month: e.target.value,
                    })
                  }
                  className="border rounded px-3 py-2"
                />
              </div>
              <div className="mt-4">
                <div className="flex flex-col gap-3">
                  {/* Checkbox for copying previous month data */}
                  <label className="flex items-center gap-2 text-sm">
                    <input
                      type="checkbox"
                      checked={copyFromPrevious}
                      onChange={(e) => setCopyFromPrevious(e.target.checked)}
                      className="rounded"
                    />
                    <span>複製目前月份資料</span>
                  </label>

                  <button
                    onClick={() => handleCreatePaymentRequest(copyFromPrevious)}
                    disabled={isLoadingMutation}
                    className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 flex items-center gap-2"
                  >
                    {isLoadingMutation ? (
                      <Loader2 className="animate-spin" size={16} />
                    ) : copyFromPrevious ? (
                      <Copy size={16} />
                    ) : (
                      <Plus size={16} />
                    )}
                    {copyFromPrevious ? "新增請款單（複製上月）" : "新增請款單"}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={handleAddPayment}
                disabled={!selectedPaymentRequest || isLoadingMutation}
                className="bg-blue-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-blue-600 disabled:opacity-50"
              >
                <Plus size={16} />
                新增付款項目
              </button>
              <button
                onClick={() => setShowBankAccounts(!showBankAccounts)}
                disabled={!selectedPaymentRequest}
                className="bg-yellow-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-yellow-600 disabled:opacity-50"
              >
                <Banknote size={16} />
                管理帳戶
              </button>
              <button
                onClick={exportToCSV}
                disabled={!selectedPaymentRequest}
                className="bg-green-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-green-600 disabled:opacity-50"
              >
                <Download size={16} />
                匯出CSV
              </button>
              <button
                onClick={handlePrintSummary}
                disabled={!selectedPaymentRequest}
                className="bg-purple-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-purple-600 disabled:opacity-50"
              >
                <Printer size={16} />
                列印摘要
              </button>
              <button
                onClick={handlePrintPaymentRequestForms}
                disabled={!selectedPaymentRequest}
                className="bg-orange-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-orange-600 disabled:opacity-50"
              >
                <Printer size={16} />
                列印請款單
              </button>
              <button
                onClick={handlePrintRemittanceSlips}
                disabled={!selectedPaymentRequest}
                className="bg-red-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-red-600 disabled:opacity-50"
              >
                <Printer size={16} />
                列印匯款憑證
              </button>
            </div>
          </div>
        </div>
      </div>

      {showBankAccounts && selectedPaymentRequest && (
        <div className="mb-6 bg-white rounded-lg shadow-md p-6 print:hidden">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">{selectedPaymentRequest.name}</h2>
            <button onClick={() => setShowBankAccounts(false)}>
              <X size={20} />
            </button>
          </div>
          <div className="flex flex-col justify-center items-center gap-6">
            <div className="w-full">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-semibold">銀行帳戶管理</h3>
                <button
                  onClick={handleAddBankAccount}
                  className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center gap-2"
                >
                  <Plus size={16} /> 新增帳戶
                </button>
              </div>
              <div className="overflow-y-auto max-h-96">
                <table className="w-full text-sm border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border p-2 text-left">戶名</th>
                      <th className="border p-2 text-left">銀行名稱</th>
                      <th className="border p-2 text-left">銀行代號</th>
                      <th className="border p-2 text-left">分行</th>
                      <th className="border p-2 text-left">帳號</th>
                      <th className="border p-2 text-left">類型</th>
                      <th className="border p-2 text-center">預設</th>
                      <th className="border p-2 text-center">啟用</th>
                      <th className="border p-2">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* New account row - shown when adding a new account */}
                    {isAddingNewAccount && (
                      <tr className="bg-yellow-50">
                        <td className="border p-2">
                          <input
                            type="text"
                            value={bankAccountForm.accountName || ""}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                accountName: e.target.value,
                              })
                            }
                            placeholder="戶名"
                            className="w-full border rounded p-1 text-sm bg-white"
                            autoFocus
                          />
                        </td>
                        <td className="border p-2">
                          <input
                            type="text"
                            value={bankAccountForm.bankName || ""}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                bankName: e.target.value,
                              })
                            }
                            placeholder="銀行名稱"
                            className="w-full border rounded p-1 text-sm bg-white"
                          />
                        </td>
                        <td className="border p-2">
                          <input
                            type="text"
                            value={bankAccountForm.bankCode || ""}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                bankCode: e.target.value,
                              })
                            }
                            placeholder="銀行代號"
                            className="w-full border rounded p-1 text-sm bg-white"
                          />
                        </td>
                        <td className="border p-2">
                          <input
                            type="text"
                            value={bankAccountForm.branchName || ""}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                branchName: e.target.value,
                              })
                            }
                            placeholder="分行"
                            className="w-full border rounded p-1 text-sm bg-white"
                          />
                        </td>
                        <td className="border p-2">
                          <input
                            type="text"
                            value={bankAccountForm.accountNumber || ""}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                accountNumber: e.target.value,
                              })
                            }
                            placeholder="帳號"
                            className="w-full border rounded p-1 text-sm bg-white"
                          />
                        </td>
                        <td className="border p-2">
                          <select
                            value={bankAccountForm.accountType || PaymentAccountType.PAYEE}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                accountType: e.target.value as PaymentAccountType,
                              })
                            }
                            className="w-full border rounded p-1 text-sm bg-white"
                          >
                            <option value={PaymentAccountType.PAYEE}>受款人</option>
                            <option value={PaymentAccountType.REMITTER}>匯款人</option>
                            <option value={PaymentAccountType.BOTH}>兩者皆可</option>
                          </select>
                        </td>
                        <td className="border p-2 text-center">
                          <input
                            type="checkbox"
                            checked={bankAccountForm.isDefault || false}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                isDefault: e.target.checked,
                              })
                            }
                            className="cursor-pointer"
                          />
                        </td>
                        <td className="border p-2 text-center">
                          <input
                            type="checkbox"
                            checked={bankAccountForm.isActive !== undefined ? bankAccountForm.isActive : true}
                            onChange={(e) =>
                              setBankAccountForm({
                                ...bankAccountForm,
                                isActive: e.target.checked,
                              })
                            }
                            className="cursor-pointer"
                          />
                        </td>
                        <td className="border p-2">
                          <div className="flex gap-1 justify-center">
                            <button
                              onClick={handleSaveAccountEdit}
                              disabled={isLoadingMutation}
                              className="bg-green-500 text-white p-1 rounded hover:bg-green-600 disabled:opacity-50"
                            >
                              <Save size={14} />
                            </button>
                            <button
                              onClick={cancelAccountEdit}
                              disabled={isLoadingMutation}
                              className="bg-gray-500 text-white p-1 rounded hover:bg-gray-600 disabled:opacity-50"
                            >
                              <X size={14} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    )}
                    {selectedPaymentRequest.paymentAccounts.map((acc) => (
                      <tr key={acc.id} className="hover:bg-gray-50">
                        <td 
                          className="border p-2 cursor-pointer font-bold"
                          onClick={() => handleAccountCellClick(acc, "accountName")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "accountName" || isAccountRowEditing) ? (
                            <input
                              type="text"
                              value={accountEditForm.accountName || ""}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  accountName: e.target.value,
                                })
                              }
                              onBlur={() => {
                                if (!isAccountRowEditing) handleSaveAccountEdit();
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") handleSaveAccountEdit();
                                if (e.key === "Escape") cancelAccountEdit();
                              }}
                              className="w-full border rounded p-1 text-sm bg-green-100"
                            />
                          ) : (
                            acc.accountName
                          )}
                        </td>
                        <td 
                          className="border p-2 cursor-pointer font-bold"
                          onClick={() => handleAccountCellClick(acc, "bankName")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "bankName" || isAccountRowEditing) ? (
                            <input
                              type="text"
                              value={accountEditForm.bankName || ""}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  bankName: e.target.value,
                                })
                              }
                              onBlur={() => {
                                if (!isAccountRowEditing) handleSaveAccountEdit();
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") handleSaveAccountEdit();
                                if (e.key === "Escape") cancelAccountEdit();
                              }}
                              className="w-full border rounded p-1 text-sm bg-green-100"
                            />
                          ) : (
                            acc.bankName
                          )}
                        </td>
                        <td 
                          className="border p-2 cursor-pointer font-bold"
                          onClick={() => handleAccountCellClick(acc, "bankCode")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "bankCode" || isAccountRowEditing) ? (
                            <input
                              type="text"
                              value={accountEditForm.bankCode || ""}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  bankCode: e.target.value,
                                })
                              }
                              onBlur={() => {
                                if (!isAccountRowEditing) handleSaveAccountEdit();
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") handleSaveAccountEdit();
                                if (e.key === "Escape") cancelAccountEdit();
                              }}
                              className="w-full border rounded p-1 text-sm bg-green-100"
                            />
                          ) : (
                            acc.bankCode
                          )}
                        </td>
                        <td 
                          className="border p-2 cursor-pointer font-bold"
                          onClick={() => handleAccountCellClick(acc, "branchName")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "branchName" || isAccountRowEditing) ? (
                            <input
                              type="text"
                              value={accountEditForm.branchName || ""}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  branchName: e.target.value,
                                })
                              }
                              onBlur={() => {
                                if (!isAccountRowEditing) handleSaveAccountEdit();
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") handleSaveAccountEdit();
                                if (e.key === "Escape") cancelAccountEdit();
                              }}
                              className="w-full border rounded p-1 text-sm bg-green-100"
                            />
                          ) : (
                            acc.branchName
                          )}
                        </td>
                        <td 
                          className="border p-2 cursor-pointer font-bold"
                          onClick={() => handleAccountCellClick(acc, "accountNumber")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "accountNumber" || isAccountRowEditing) ? (
                            <input
                              type="text"
                              value={accountEditForm.accountNumber || ""}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  accountNumber: e.target.value,
                                })
                              }
                              onBlur={() => {
                                if (!isAccountRowEditing) handleSaveAccountEdit();
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") handleSaveAccountEdit();
                                if (e.key === "Escape") cancelAccountEdit();
                              }}
                              className="w-full border rounded p-1 text-sm bg-green-100"
                            />
                          ) : (
                            acc.accountNumber
                          )}
                        </td>
                        <td 
                          className="border p-2 cursor-pointer font-bold"
                          onClick={() => handleAccountCellClick(acc, "accountType")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "accountType" || isAccountRowEditing) ? (
                            <select
                              value={accountEditForm.accountType || ""}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  accountType: e.target.value as PaymentAccountType,
                                })
                              }
                              onBlur={() => {
                                if (!isAccountRowEditing) handleSaveAccountEdit();
                              }}
                              className="w-full border rounded p-1 text-sm bg-green-100"
                            >
                              <option value={PaymentAccountType.PAYEE}>受款人</option>
                              <option value={PaymentAccountType.REMITTER}>匯款人</option>
                              <option value={PaymentAccountType.BOTH}>兩者皆可</option>
                            </select>
                          ) : (
                            acc.accountType
                          )}
                        </td>
                        <td 
                          className="border p-2 text-center cursor-pointer"
                          onClick={() => handleAccountCellClick(acc, "isDefault")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "isDefault" || isAccountRowEditing) ? (
                            <input
                              type="checkbox"
                              checked={accountEditForm.isDefault || false}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  isDefault: e.target.checked,
                                })
                              }
                              className="cursor-pointer"
                            />
                          ) : (
                            acc.isDefault ? "✓" : ""
                          )}
                        </td>
                        <td 
                          className="border p-2 text-center cursor-pointer"
                          onClick={() => handleAccountCellClick(acc, "isActive")}
                        >
                          {editingAccountId === acc.id && (editingAccountField === "isActive" || isAccountRowEditing) ? (
                            <input
                              type="checkbox"
                              checked={accountEditForm.isActive || false}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) =>
                                setAccountEditForm({
                                  ...accountEditForm,
                                  isActive: e.target.checked,
                                })
                              }
                              className="cursor-pointer"
                            />
                          ) : (
                            acc.isActive ? "✓" : ""
                          )}
                        </td>
                        <td className="border p-2">
                          {editingAccountId === acc.id ? (
                            <div className="flex gap-1 justify-center">
                              <button
                                onClick={handleSaveAccountEdit}
                                disabled={isLoadingMutation}
                                className="bg-green-500 text-white p-1 rounded hover:bg-green-600 disabled:opacity-50"
                              >
                                <Save size={14} />
                              </button>
                              <button
                                onClick={cancelAccountEdit}
                                disabled={isLoadingMutation}
                                className="bg-gray-500 text-white p-1 rounded hover:bg-gray-600 disabled:opacity-50"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          ) : (
                            <div className="flex gap-1 justify-center">
                              <button
                                onClick={() => handleAccountRowEdit(acc.id, acc)}
                                disabled={isLoadingMutation}
                                className="bg-blue-500 text-white p-1 rounded hover:bg-blue-600 disabled:opacity-50"
                              >
                                <Edit2 size={14} />
                              </button>
                              <button
                                onClick={() => handleDeleteBankAccount(acc.id)}
                                disabled={isLoadingMutation}
                                className="bg-red-500 text-white p-1 rounded hover:bg-red-600 disabled:opacity-50"
                              >
                                <Trash2 size={14} />
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* --- VISIBLE & HIDDEN CONTENT AREA --- */}
      {selectedPaymentRequest && (
        <>
          {/* SECTION 1: VISIBLE Interactive Table (for UI) */}
          <div className="bg-white rounded-lg mb-6">
            <div className="p-6">
              <div className="flex flex-rows justify-center items-center">
                <div
                  className="text-center text-3xl font-bold mt-2 mb-6"
                  style={{ letterSpacing: "0.3em", lineHeight: "1.5" }}
                >
                  {selectedPaymentRequest.name}
                  {selectedPaymentRequest.year}年{selectedPaymentRequest.month}
                  月份請款明細表
                </div>
                {/* Delete button - only show if a payment request is selected */}
                {selectedPaymentRequest && (
                  <button
                    onClick={() => openDeleteModal(selectedPaymentRequest)}
                    className="print:hidden bg-red-500 text-white p-2 rounded hover:bg-red-600 flex items-center mb-4 gap-2"
                  >
                    <Trash2 size={16} />
                  </button>
                )}
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300 text-sm print:text-lg">
                  {/* ... table head ... */}
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="w-12 border p-2 print:hidden">
                        <input
                          className="hover:cursor-pointer"
                          type="checkbox"
                          checked={isAllSelected}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                        />
                      </th>
                      <th className="w-16 border p-2">序號</th>
                      <th className="w-70 border p-2">會計科目</th>
                      <th className="w-54 border p-2">受款人</th>
                      <th className="w-40 border p-2 print:hidden">銀行備註</th>
                      <th className="border p-2">月份</th>
                      <th className="w-28 border p-2">金額</th>
                      <th className="w-22 border p-2">付款方式</th>
                      <th className="w-24 border p-2 print:hidden">
                        <input
                          type="checkbox"
                          checked={!!showAccountInfoAll}
                          onChange={() =>
                            handleToggleAccountInfoAll(
                              selectedPaymentRequest.id,
                              showAccountInfoAll
                            )
                          }
                          className="mr-1"
                          disabled={toggleAccountInfoAllMutation.isPending}
                          ref={(el) => {
                            if (el) {
                              // Handle indeterminate state for partial selection
                              const payments =
                                selectedPaymentRequest?.payments || [];
                              const someTrue = payments.some(
                                (p) => p.showAccountInfo
                              );
                              const someFalse = payments.some(
                                (p) => !p.showAccountInfo
                              );
                              el.indeterminate = someTrue && someFalse;
                            }
                          }}
                        />
                        帳戶資訊
                      </th>

                      <th className="border p-2 print:hidden">操作</th>
                    </tr>
                  </thead>
                  {/* ... table body with editing logic ... */}
                  <tbody>
                    {selectedPaymentRequest.payments.map((payment) => (
                      <React.Fragment key={payment.id}>
                        <tr>
                          <td className="border p-2 print:hidden">
                            <div className="flex gap-1 justify-center">
                              <input
                                className="hover:cursor-pointer"
                                type="checkbox"
                                checked={!!selectedPayments[payment.id]}
                                onChange={(e) => handleSelectPayment(payment.id, e.target.checked)}
                              />
                            </div>
                          </td>
                          <td
                            className="border p-2 font-bold cursor-pointer"
                            onClick={() =>
                              handleCellClick(payment, "sequenceId")
                            }
                          >
                            {editingId === payment.id &&
                            (editingField === "sequenceId" || isRowEditing) ? (
                              <input
                                type="text"
                                value={editForm.sequenceId || ""}
                                onClick={(e) => e.stopPropagation()}
                                onChange={(e) =>
                                  setEditForm({
                                    ...editForm,
                                    sequenceId: e.target.value,
                                  })
                                }
                                onBlur={() => {
                                  if (!isRowEditing) handleSaveEdit(); // only auto-save in cell mode
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") handleSaveEdit(); // ✅ Save on Enter
                                  if (e.key === "Escape") cancelEdit(); // Optional: cancel on Esc
                                }}
                                className="w-16 border rounded p-1 text-sm mx-auto bg-green-100"
                              />
                            ) : (
                              payment.sequenceId
                            )}
                          </td>
                          <td
                            className="border p-2 font-bold cursor-pointer"
                            onClick={() =>
                              handleCellClick(payment, "accountingSubject")
                            }
                          >
                            {editingId === payment.id &&
                            (editingField === "accountingSubject" ||
                              isRowEditing) ? (
                              <textarea
                                value={editForm.accountingSubject || ""}
                                onClick={(e) => e.stopPropagation()}
                                onChange={(e) =>
                                  setEditForm({
                                    ...editForm,
                                    accountingSubject: e.target.value,
                                  })
                                }
                                onBlur={() => {
                                  if (!isRowEditing) handleSaveEdit(); // only auto-save in cell mode
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") handleSaveEdit(); // ✅ Save on Enter
                                  if (e.key === "Escape") cancelEdit(); // Optional: cancel on Esc
                                }}
                                className="w-fit border rounded p-1 text-sm bg-green-100"
                              />
                            ) : (
                              payment.accountingSubject
                            )}
                          </td>

                          <td
                            className="border p-2 font-bold cursor-pointer"
                            onClick={() => handleCellClick(payment, "payee")}
                          >
                            {editingId === payment.id &&
                            (editingField === "payee" || isRowEditing) ? (
                              <div onClick={(e) => e.stopPropagation()}>
                                <Combobox
                                  options={PAYEE_OPTIONS}
                                  value={editForm.payee || ""}
                                  onValueChange={async (value) => {
                                    // Update the form first
                                    setEditForm({
                                      ...editForm,
                                      payee: value,
                                    });

                                    // Then immediately look up the payee account
                                    if (value && value.length > 1) {
                                      console.log(
                                        "Looking up payee account for:",
                                        value
                                      );
                                      try {
                                        const result =
                                          await getPaymentAccounts(value);
                                        if (
                                          result.success &&
                                          result.data &&
                                          result.data.length > 0
                                        ) {
                                          const bestMatch = result.data.find(
                                            (acc: PaymentAccount) =>
                                              acc.accountName.includes(value)
                                          );

                                          if (bestMatch) {
                                            console.log(
                                              "Found matching account:",
                                              bestMatch
                                            );
                                            setEditForm((currentForm) => ({
                                              ...currentForm,
                                              payee: value, // Make sure payee is set
                                              payeeAccountId: bestMatch.id,
                                            }));
                                          }
                                        }
                                      } catch (error) {
                                        console.error(
                                          "Error looking up payee account:",
                                          error
                                        );
                                      }
                                    }
                                  }}
                                  placeholder="選擇受款人"
                                  searchPlaceholder="搜尋或輸入新的受款人..."
                                  allowCustom={true}
                                  className="w-full"
                                  onOpenChange={(open) => {
                                    console.log(
                                      "Combobox open state changed:",
                                      open
                                    );
                                    isComboboxFocused.current = open;
                                  }}
                                  onBlur={() => {
                                    console.log("Combobox onBlur triggered");
                                    isComboboxFocused.current = false;
                                    // Add delay to allow for click events to process
                                    setTimeout(() => {
                                      if (
                                        !isComboboxFocused.current &&
                                        !isRowEditing
                                      ) {
                                        handleSaveEdit();
                                      }
                                    }, 150);
                                  }}
                                  onKeyDown={(e) => {
                                    console.log("Combobox key pressed:", e.key);
                                    if (e.key === "Enter") handleSaveEdit();
                                    if (e.key === "Escape") cancelEdit();
                                  }}
                                />
                              </div>
                            ) : (
                              payment.payee
                            )}
                          </td>

                          <td
                            className="border p-2 font-bold print:hidden"
                            onClick={() => handleCellClick(payment, "remarks")}
                          >
                            {editingId === payment.id &&
                            (editingField === "remarks" || isRowEditing) ? (
                              <div onClick={(e) => e.stopPropagation()}>
                                <Combobox
                                  options={REMARKS_OPTIONS}
                                  value={editForm.remarks || ""}
                                  onValueChange={(value) =>
                                    setEditForm({
                                      ...editForm,
                                      remarks: value,
                                    })
                                  }
                                  placeholder="請選擇銀行備註"
                                  searchPlaceholder="搜尋備註..."
                                  allowCustom={true}
                                  className="w-full"
                                  onOpenChange={(open) => {
                                    console.log(
                                      "Combobox open state changed:",
                                      open
                                    );
                                    isComboboxFocused.current = open;
                                  }}
                                  onBlur={() => {
                                    console.log("Combobox onBlur triggered");
                                    isComboboxFocused.current = false;
                                    // Add delay to allow for click events to process
                                    setTimeout(() => {
                                      if (
                                        !isComboboxFocused.current &&
                                        !isRowEditing
                                      ) {
                                        handleSaveEdit();
                                      }
                                    }, 150);
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.key === "Enter") handleSaveEdit();
                                    if (e.key === "Escape") cancelEdit();
                                  }}
                                />
                              </div>
                            ) : (
                              payment.remarks
                            )}
                          </td>

                          <td
                            className="border p-2 text-center font-bold"
                            onClick={() => handleCellClick(payment, "month")}
                          >
                            {editingId === payment.id &&
                            (editingField === "month" || isRowEditing) ? (
                              <input
                                type="text"
                                value={editForm.month || ""}
                                onClick={(e) => e.stopPropagation()}
                                onChange={(e) =>
                                  setEditForm({
                                    ...editForm,
                                    month: e.target.value,
                                  })
                                }
                                onBlur={() => {
                                  if (!isRowEditing) handleSaveEdit(); // only auto-save in cell mode
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") handleSaveEdit(); // ✅ Save on Enter
                                  if (e.key === "Escape") cancelEdit(); // Optional: cancel on Esc
                                }}
                                className="w-24 border rounded p-1 text-sm mx-auto bg-green-100"
                              />
                            ) : (
                              payment.month
                            )}
                          </td>
                          <td
                            className="border p-2 text-right font-bold"
                            onClick={() => handleCellClick(payment, "amount")}
                          >
                            {editingId === payment.id &&
                            (editingField === "amount" || isRowEditing) ? (
                              <input
                                type="number"
                                value={editForm.amount || 0}
                                onClick={(e) => e.stopPropagation()}
                                onChange={(e) =>
                                  setEditForm({
                                    ...editForm,
                                    amount: parseInt(e.target.value) || 0,
                                  })
                                }
                                onBlur={() => {
                                  if (!isRowEditing) handleSaveEdit(); // only auto-save in cell mode
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") handleSaveEdit(); // ✅ Save on Enter
                                  if (e.key === "Escape") cancelEdit(); // Optional: cancel on Esc
                                }}
                                className="w-28 border rounded p-1 text-sm text-right bg-green-100"
                              />
                            ) : (
                              `${payment.amount.toLocaleString()}`
                            )}
                          </td>
                          <td
                            className="border p-2 text-center font-bold"
                            onClick={() =>
                              handleCellClick(payment, "paymentMethod")
                            }
                          >
                            {editingId === payment.id &&
                            (editingField === "paymentMethod" ||
                              isRowEditing) ? (
                              <select
                                value={editForm.paymentMethod || ""}
                                onClick={(e) => e.stopPropagation()}
                                onChange={(e) =>
                                  setEditForm({
                                    ...editForm,
                                    paymentMethod: e.target.value,
                                  })
                                }
                                onBlur={() => {
                                  if (!isRowEditing) handleSaveEdit(); // only auto-save in cell mode
                                }}
                                className="w-fit border rounded p-1 text-sm bg-green-100"
                              >
                                <option value="匯款">匯款</option>
                                <option value="領現">領現</option>
                                <option value="轉帳">轉帳</option>
                                <option value="存款">存款</option>
                                <option value="自動扣款">自動扣款</option>
                              </select>
                            ) : (
                              <Badge
                                variant={
                                  payment.paymentMethod === "匯款"
                                    ? "remittance"
                                    : payment.paymentMethod === "領現"
                                      ? "cash"
                                      : payment.paymentMethod === "轉帳"
                                        ? "transfer"
                                        : payment.paymentMethod === "存款"
                                          ? "deposit"
                                          : payment.paymentMethod === "自動扣款"
                                            ? "autopay"
                                            : "secondary"
                                }
                              >
                                {payment.paymentMethod}
                              </Badge>
                            )}
                          </td>
                          <td className="border p-2 print:hidden">
                            {" "}
                            <div className="flex justify-center gap-1">
                              {" "}
                              <input
                                type="checkbox"
                                checked={!!payment.showAccountInfo}
                                onChange={() =>
                                  handleToggleAccountInfo(payment)
                                }
                                className="mr-1 bg-green-100"
                                disabled={isLoadingMutation}
                              />{" "}
                              顯示{" "}
                            </div>{" "}
                          </td>

                          <td className="border p-2 print:hidden">
                            {editingId === payment.id ? (
                              <div className="flex gap-1">
                                <button
                                  onClick={handleSaveEdit}
                                  disabled={isLoadingMutation}
                                  className="bg-green-500 text-white p-1 rounded hover:bg-green-600 disabled:opacity-50"
                                >
                                  <Save size={14} />
                                </button>
                                <button
                                  onClick={cancelEdit}
                                  disabled={isLoadingMutation}
                                  className="bg-gray-500 text-white p-1 rounded hover:bg-gray-600 disabled:opacity-50"
                                >
                                  <X size={14} />
                                </button>
                              </div>
                            ) : (
                              <div className="flex justify-center gap-1">
                                <button
                                  onClick={() => {
                                    handleRowEdit(payment.id, payment);
                                  }}
                                  disabled={isLoadingMutation}
                                  className="bg-blue-500 text-white p-1 rounded hover:bg-blue-600 disabled:opacity-50"
                                >
                                  <Edit2 size={14} />
                                </button>
                                <button
                                  onClick={() =>
                                    handleDeletePayment(payment.id)
                                  }
                                  disabled={isLoadingMutation}
                                  className="bg-red-500 text-white p-1 rounded hover:bg-red-600 disabled:opacity-50"
                                >
                                  <Trash2 size={14} />
                                </button>
                              </div>
                            )}
                          </td>
                        </tr>
                        {payment.showAccountInfo &&
                          editingId !== payment.id &&
                          payment.payeeAccount && (
                            <tr className="bg-gray-50 print:hidden">
                              <td className="border p-2" colSpan={8}>
                                <div className="grid grid-cols-5 gap-4 text-xs">
                                  <div>
                                    <strong>受款銀行:</strong>{" "}
                                    {payment.payeeAccount.bankName} (
                                    {payment.payeeAccount.bankCode})
                                  </div>
                                  <div>
                                    <strong>分行:</strong>{" "}
                                    {payment.payeeAccount.branchName}
                                  </div>
                                  <div>
                                    <strong>帳號:</strong>{" "}
                                    {payment.payeeAccount.accountNumber}
                                  </div>
                                  <div>
                                    <strong>戶名:</strong>{" "}
                                    {payment.payeeAccount.accountName}
                                  </div>
                                </div>
                              </td>
                            </tr>
                          )}
                        {editingId === payment.id && (
                          <tr className="print:hidden bg-green-50">
                            <td className="border p-2" colSpan={8}>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="block text-xs font-semibold mb-1">
                                    匯款帳戶 (留空使用預設)
                                  </label>
                                  <select
                                    value={editForm.remitterAccountId || ""}
                                    onChange={(e) =>
                                      setEditForm({
                                        ...editForm,
                                        remitterAccountId:
                                          e.target.value || undefined,
                                      })
                                    }
                                    className="w-full border rounded p-1 text-xs"
                                  >
                                    <option value="">使用請款單預設</option>
                                    {remitterAccounts.map((acc) => (
                                      <option key={acc.id} value={acc.id}>
                                        {acc.accountName} ({acc.bankName}{" "}
                                        {acc.accountNumber})
                                      </option>
                                    ))}
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-xs font-semibold mb-1">
                                    受款帳戶
                                  </label>
                                  <select
                                    value={editForm.payeeAccountId || ""}
                                    onChange={(e) =>
                                      setEditForm({
                                        ...editForm,
                                        payeeAccountId:
                                          e.target.value || undefined,
                                      })
                                    }
                                    className="w-full border rounded p-1 text-xs"
                                  >
                                    <option value="">選擇受款帳戶</option>
                                    {payeeAccounts.map((acc) => (
                                      <option key={acc.id} value={acc.id}>
                                        {acc.accountName} ({acc.bankName}{" "}
                                        {acc.accountNumber})
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                  </tbody>
                  {/* ... table foot ... */}
                  <tfoot>
                    <tr className="bg-gray-100 font-bold">
                      <td className="border p-2" colSpan={5}>
                        合計
                      </td>
                      <td className="border p-2 text-right print:text-lg">
                        ${totalAmount.toLocaleString()}
                      </td>
                      <td className="border p-2" colSpan={5}></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              {/* Signature section */}
              <div className="hidden print:block mt-2 border-2 border-gray-300">
                <div className="grid grid-cols-4 text-center text-sm print:text-lg font-bold">
                  <div className="border-r-2 border-gray-300 py-1 h-48 flex flex-col">
                    <div className="border-b-2 border-gray-300 mb-8">
                      主任委員
                    </div>
                    <div className="flex-1 flex items-end justify-center pb-2">
                      {/* Placeholder for stamp */}
                      <div className="w-16 h-24"></div>
                    </div>
                  </div>
                  <div className="border-r-2 border-gray-300 py-1 h-48 flex flex-col">
                    <div className="border-b-2 border-gray-300 mb-8">
                      監察委員
                    </div>
                    <div className="flex-1 flex items-end justify-center pb-2">
                      <div className="w-16 h-24"></div>
                    </div>
                  </div>
                  <div className="border-r-2 border-gray-300 py-1 h-48 flex flex-col">
                    <div className="border-b-2 border-gray-300 mb-8">
                      財務委員
                    </div>
                    <div className="flex-1 flex items-end justify-center pb-2">
                      <div className="w-16 h-24"></div>
                    </div>
                  </div>
                  <div className="py-1 h-48 flex flex-col">
                    <div className="border-b-2 border-gray-300 mb-8">
                      承辦人
                    </div>
                    <div className="flex-1 flex items-end justify-center pb-2">
                      <div className="w-20 h-18 border-2 border-red-500 rounded flex items-center justify-center text-sm text-red-600">
                        行政總幹事
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* SECTION 2: HIDDEN container for ALL print formats */}
          <div style={{ display: "none" }}>
            {/* A. Hidden Summary Table for Printing */}
            <div ref={summaryPrintRef}>
              <div className="p-6">
                <div className="text-center text-3xl font-bold mt-2 mb-6">
                  {selectedPaymentRequest.name}
                  {selectedPaymentRequest.year}年{selectedPaymentRequest.month}
                  月份請款明細表
                </div>
                <table className="w-full border border-2 border-black text-sm print:text-lg">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="w-16 border border-black p-2">序號</th>
                      <th className="w-70 border border-black p-2">會計科目</th>
                      <th className="w-54 border border-black p-2">受款人</th>
                      <th className="border border-black p-2">月份</th>
                      <th className="w-28 border border-black p-2">金額</th>
                      <th className="w-22 border border-black p-2">付款方式</th>
                    </tr>
                  </thead>
                  <tbody>
                    {printablePayments.map((payment) => (
                      <tr key={payment.id}>
                        <td className="border border-black p-2 font-bold">{payment.sequenceId}</td>
                        <td className="border border-black p-2 font-bold whitespace-pre-line">{payment.accountingSubject}</td>
                        <td className="border border-black p-2 font-bold">{payment.payee}</td>
                        <td className="border border-black p-2 text-center font-bold">{payment.month}</td>
                        <td className="border border-black p-2 text-right font-bold">{payment.amount.toLocaleString()}</td>
                        <td className="border border-black p-2 text-center font-bold">{payment.paymentMethod}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-100 font-bold">
                      <td className="border border-black p-2" colSpan={4}>合計</td>
                      <td className="border border-black p-2 text-right print:text-lg">
                        $ {printablePayments.reduce((sum, p) => sum + p.amount, 0).toLocaleString()}
                      </td>
                      <td className="border border-black p-2"></td>
                    </tr>
                  </tfoot>
                </table>
                 <div className="hidden print:block mt-2 border-2 border-gray-300">
                    <div className="grid grid-cols-4 text-center text-sm print:text-lg font-bold">
                      <div className="border-r-2 border-gray-300 py-1 h-48 flex flex-col">
                        <div className="border-b-2 border-gray-300 mb-8">主任委員</div>
                        <div className="flex-1 flex items-end justify-center pb-2"><div className="w-16 h-24"></div></div>
                      </div>
                      <div className="border-r-2 border-gray-300 py-1 h-48 flex flex-col">
                        <div className="border-b-2 border-gray-300 mb-8">監察委員</div>
                        <div className="flex-1 flex items-end justify-center pb-2"><div className="w-16 h-24"></div></div>
                      </div>
                      <div className="border-r-2 border-gray-300 py-1 h-48 flex flex-col">
                        <div className="border-b-2 border-gray-300 mb-8">財務委員</div>
                        <div className="flex-1 flex items-end justify-center pb-2"><div className="w-16 h-24"></div></div>
                      </div>
                      <div className="py-1 h-48 flex flex-col">
                        <div className="border-b-2 border-gray-300 mb-8">承辦人</div>
                        <div className="flex-1 flex items-end justify-center pb-2"><div className="w-20 h-18 border-2 border-red-500 rounded flex items-center justify-center text-sm text-red-600">行政總幹事</div></div>
                      </div>
                    </div>
                 </div>
              </div>
            </div>
            <div ref={paymentRequestFormsPrintRef}>
              {printablePayments.map((payment) =>
                generatePaymentRequestForm(
                  payment,
                  selectedPaymentRequest,
                  defaultRemitterAccount
                )
              )}
            </div>
            <div
              ref={remittanceSlipsPrintRef}
              style={{
                width: "21cm",
                height: "24.7cm",
                padding: "0.2cm 0.2cm",
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "column",
                fontFamily: "serif",
                fontSize: "20px",
                margin: "0 auto",
              }}
            >
              {printablePayments.map((payment, index) => (
                <div
                  key={payment.id}
                  className={(index + 1) % 4 === 0 ? "page-break" : ""}
                >
                  {generateRemittanceSlip(payment, defaultRemitterAccount)}
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      <style jsx global>{`
        .border-b.border-dashed {
          border-bottom-style: dashed !important;
          border-bottom-width: 1px !important;
          border-bottom-color: #e2e8f0 !important;
        }
        .text-red-600 {
          color: #dc2626 !important;
        }
        .page-break {
          page-break-after: always;
        }
      `}</style>
    </div>
  );
};

export default PaymentManager;
