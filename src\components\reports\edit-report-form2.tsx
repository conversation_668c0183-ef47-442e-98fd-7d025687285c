"use client";

import { useEffect, useState } from "react";
import { useActionState } from "react";
import { useRouter } from "next/navigation";
import { MeetingReport } from "@/lib/types";
import { toast } from "sonner";

import { updateReport } from "@/actions/reports/update-report";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Blocknote imports
import { useCreateBlockNote } from "@blocknote/react";
import dynamic from 'next/dynamic';
import "@blocknote/core/style.css";

// Dynamically import the BlockNoteEditor with no SSR
const BlockNoteEditor = dynamic(() => import("@/components/reports/block-note-editor"), {
  ssr: false,
  loading: () => (
    <div className="border rounded-lg min-h-[400px] flex items-center justify-center">
      Loading editor...
    </div>
  ),
});

// 定義明確的狀態類型
type UpdateReportState = 
  | { success: false; error: string; issues?: undefined; updatedSlug?: undefined }
  | { success: false; error: string; issues: { 
      title?: string[];
      content?: string[];
      reportDate?: string[];
      reportType?: string[];
      visibility?: string[];
      published?: string[];
      workspaceId?: string[];
      reportId?: string[];
    }; updatedSlug?: undefined }
  | { success: true; error: ""; issues?: undefined; updatedSlug?: string };

interface EditReportFormProps {
  report: MeetingReport;
}

export function EditReportForm({ report }: EditReportFormProps) {
  const [htmlContent, setHtmlContent] = useState(report.content);
  
  // 修正 initialState 類型
  const initialState: UpdateReportState = { 
    success: false, 
    error: "", 
    issues: undefined, 
    updatedSlug: undefined 
  };
  
  const [state, dispatch] = useActionState(updateReport, initialState);
  const router = useRouter();

  // Remove editor instance creation and effects as they're now handled in BlockNoteEditor

  useEffect(() => {
    if (state.success) {
      toast.success("Report updated successfully.");
      // Redirect to the detail page of the report
      router.push(`/workspaces/${report.workspaceId}/reports/${state.updatedSlug || report.slug}`);
    } else if (state.error) {
      toast.error(state.error);
    }
  }, [state, router, report.workspaceId, report.slug]);

  // Helper to format date for input
  const formatDateForInput = (date: Date) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (`0${d.getMonth() + 1}`).slice(-2);
    const day = (`0${d.getDate()}`).slice(-2);
    return `${year}-${month}-${day}`;
  };

  const handleContentChange = (html: string) => {
    setHtmlContent(html);
  };

  return (
    <form action={dispatch} className="space-y-6 max-w-4xl">
      <input type="hidden" name="workspaceId" value={report.workspaceId} />
      <input type="hidden" name="reportId" value={report.id} />
      <input type="hidden" name="content" value={htmlContent} />
      
      <div className="space-y-2">
        <Label htmlFor="title">Report Title</Label>
        <Input id="title" name="title" defaultValue={report.title} required />
        {state.issues?.title && <p className="text-sm text-red-500">{state.issues.title[0]}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="reportDate">Report Date</Label>
          <Input id="reportDate" name="reportDate" type="date" defaultValue={formatDateForInput(report.reportDate)} required />
          {state.issues?.reportDate && <p className="text-sm text-red-500">{state.issues.reportDate[0]}</p>}
        </div>
        <div className="space-y-2">
          <Label htmlFor="reportType">Report Type</Label>
          <Select name="reportType" defaultValue={report.reportType}>
            <SelectTrigger>
              <SelectValue placeholder="Select a type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="MONTHLY">Monthly</SelectItem>
              <SelectItem value="ANNUAL">Annual</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="visibility">Visibility</Label>
        <Select name="visibility" defaultValue={report.visibility}>
          <SelectTrigger>
            <SelectValue placeholder="Select visibility" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="WORKSPACE_MEMBERS">Workspace Members Only</SelectItem>
            <SelectItem value="PUBLIC">Public</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2 pt-2">
        <Switch
          id="published"
          name="published"
          defaultChecked={report.published}
        />
        <Label htmlFor="published" className="cursor-pointer">Published</Label>
      </div>
      <p className="text-sm text-muted-foreground -mt-4">
        If unchecked, the report will be saved as a draft and will not be visible to anyone except admins.
      </p>

      <div className="space-y-2">
        <Label>Report Content</Label>
        <BlockNoteEditor 
          key={report.id} // Add key to ensure clean remount when report changes
          initialContent={report.content}
          onContentChange={handleContentChange}
        />
        {state.issues?.content && <p className="text-sm text-red-500">{state.issues.content[0]}</p>}
      </div>

      <Button type="submit">
        Update Report
      </Button>
    </form>
  );
}