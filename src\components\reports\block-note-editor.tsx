"use client";

import { useState, useRef } from "react";
import { BlockNoteEditor as BlockNoteEditorType } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/shadcn";

interface BlockNoteEditorProps {
  initialContent?: string;
  onContentChange: (html: string) => void;
}

export default function BlockNoteEditor({ initialContent, onContentChange }: BlockNoteEditorProps) {
  const [isReady, setIsReady] = useState(false);
  const editorRef = useRef<BlockNoteEditorType | null>(null);
  const changeHandlerRef = useRef<(() => void) | null>(null);

  // Create editor instance
  /*useEffect(() => {
    let mounted = true;

    const init = async () => {
      try {
        if (!editorRef.current && mounted) {
          const editor = useCreateBlockNote({
            initialContent: [], // Start with empty content
            domAttributes: {
              editor: {
                class: "dark:bg-background", // Add any custom classes you need
              },
            },
          });
          
          if (!mounted) return;
          editorRef.current = editor;

          // Set up change handler
          const unsubscribe = editor.onChange(async () => {
            try {
              const html = await editor.blocksToHTMLLossy(editor.topLevelBlocks);
              onContentChange(html);
            } catch (error) {
              console.error('Failed to convert blocks to HTML:', error);
              toast.error('Failed to save changes');
            }
          });
          
          if (!mounted) {
            unsubscribe?.();
            return;
          }
          
          changeHandlerRef.current = unsubscribe || null;

          // If there's initial content, parse and set it
          if (initialContent) {
            const blocks = await editor.tryParseHTMLToBlocks(initialContent);
            if (!mounted) return;
            editor.replaceBlocks(editor.topLevelBlocks, blocks);
          }

          if (mounted) {
            setIsReady(true);
          }
        }
      } catch (error) {
        console.error('Failed to initialize editor:', error);
        if (mounted) {
          toast.error('Failed to initialize editor');
        }
      }
    };

    init();

    // Cleanup
    return () => {
      mounted = false;
      if (changeHandlerRef.current) {
        changeHandlerRef.current();
        changeHandlerRef.current = null;
      }
      if (editorRef.current) {
        editorRef.current = null;
      }
      setIsReady(false);
    };
  }, [initialContent, onContentChange]);*/

  if (!isReady || !editorRef.current) {
    return (
      <div className="border rounded-lg min-h-[400px] flex items-center justify-center">
        Loading editor...
      </div>
    );
  }

  return (
    <div className="border rounded-lg min-h-[400px] overflow-hidden">
      <BlockNoteView 
        editor={editorRef.current}
        theme="light"
      />
    </div>
  );
}

const initialContents = [
      {
        type: "paragraph",
        content: "Welcome to this demo!",
      },
      {
        type: "paragraph",
      },
      {
        type: "paragraph",
        content: [
          {
            type: "text",
            text: "Blocks:",
            styles: { bold: true },
          },
        ],
      },
      {
        type: "paragraph",
        content: "Paragraph",
      },
      {
        type: "heading",
        content: "Heading",
      },
      {
        id: "toggle-heading",
        type: "heading",
        props: { isToggleable: true },
        content: "Toggle Heading",
      },
      {
        type: "quote",
        content: "Quote",
      },
      {
        type: "bulletListItem",
        content: "Bullet List Item",
      },
      {
        type: "numberedListItem",
        content: "Numbered List Item",
      },
      {
        type: "checkListItem",
        content: "Check List Item",
      },
      {
        id: "toggle-list-item",
        type: "toggleListItem",
        content: "Toggle List Item",
      },
      {
        type: "codeBlock",
        props: { language: "javascript" },
        content: "console.log('Hello, world!');",
      },
      {
        type: "table",
        content: {
          type: "tableContent",
          rows: [
            {
              cells: ["Table Cell", "Table Cell", "Table Cell"],
            },
            {
              cells: ["Table Cell", "Table Cell", "Table Cell"],
            },
            {
              cells: ["Table Cell", "Table Cell", "Table Cell"],
            },
          ],
        },
      },
      {
        type: "file",
      },
      {
        type: "image",
        props: {
          url: "https://interactive-examples.mdn.mozilla.net/media/cc0-images/grapefruit-slice-332-332.jpg",
          caption:
            "From https://interactive-examples.mdn.mozilla.net/media/cc0-images/grapefruit-slice-332-332.jpg",
        },
      },
      {
        type: "video",
        props: {
          url: "https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.webm",
          caption:
            "From https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.webm",
        },
      },
      {
        type: "audio",
        props: {
          url: "https://interactive-examples.mdn.mozilla.net/media/cc0-audio/t-rex-roar.mp3",
          caption:
            "From https://interactive-examples.mdn.mozilla.net/media/cc0-audio/t-rex-roar.mp3",
        },
      },
      {
        type: "paragraph",
      },
      {
        type: "paragraph",
        content: [
          {
            type: "text",
            text: "Inline Content:",
            styles: { bold: true },
          },
        ],
      },
      {
        type: "paragraph",
        content: [
          {
            type: "text",
            text: "Styled Text",
            styles: {
              bold: true,
              italic: true,
              textColor: "red",
              backgroundColor: "blue",
            },
          },
          {
            type: "text",
            text: " ",
            styles: {},
          },
          {
            type: "link",
            content: "Link",
            href: "https://www.blocknotejs.org",
          },
        ],
      },
      {
        type: "paragraph",
      },
    ];