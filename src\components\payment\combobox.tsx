"use client";
import React, { useRef } from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface ComboboxProps {
  options: { value: string; label: string }[]
  value: string
  onValueChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  allowCustom?: boolean
  className?: string
  onOpenChange?: (open: boolean) => void
  onBlur?: () => void
  onKeyDown?: (e: React.KeyboardEvent) => void
}

export function Combobox({
  options,
  value,
  onValueChange,
  placeholder = "請選擇...",
  searchPlaceholder = "搜尋...",
  allowCustom = true,
  className,
  onOpenChange,
  onBlur,
  onKeyDown,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const containerRef = useRef<HTMLDivElement>(null)

  // Filter options based on search
  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchValue.toLowerCase())
  )

  const handleSelect = (selectedValue: string) => {
    onValueChange(selectedValue)
    setOpen(false)
    setSearchValue("")
  }

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && allowCustom && searchValue) {
      e.preventDefault()
      handleSelect(searchValue)
    }
    onKeyDown?.(e)
  }

  const handleOpenChange = (newOpen: boolean) => {
    console.log('Combobox open state changed:', newOpen)
    setOpen(newOpen)
    onOpenChange?.(newOpen) // Call the parent's onOpenChange
  }

  const handleBlur = (e: React.FocusEvent) => {
    console.log('Combobox handleBlur triggered')
    
    // Check if the blur is moving to another element within the combobox
    if (containerRef.current?.contains(e.relatedTarget as Node)) {
      console.log('Focus moved within combobox, not triggering onBlur')
      return // Don't trigger onBlur if focus is moving within the component
    }
    
    // Delay onBlur to allow for click events
    setTimeout(() => {
      if (!open) {
        console.log('Executing delayed onBlur')
        onBlur?.()
      }
    }, 150)
  }

  return (
    <div ref={containerRef}>
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between bg-green-100", className)}
            onBlur={handleBlur}
          >
            <span className="truncate">
              {value || placeholder}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder={searchPlaceholder}
              value={searchValue}
              onValueChange={setSearchValue}
              onKeyDown={handleInputKeyDown}
            />
            <CommandEmpty>
              {allowCustom && searchValue ? (
                <div
                  className="px-2 py-1 text-sm cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSelect(searchValue)}
                >
                  使用 "{searchValue}"
                </div>
              ) : (
                "未找到選項"
              )}
            </CommandEmpty>
            <CommandGroup>
              {filteredOptions.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}