
"use server";

import { getAccountDetails } from "@/components/reports/get-account-details";
import { auth } from "@clerk/nextjs/server";
import { COMMUNITY_CATEGORIES } from "@/lib/config/categories";

// Helper function to standardize and aggregate data
const categorizeAndAggregate = async (
  tableData: any[],
  userId: string,
  type: 'CREDIT' | 'DEBIT'
) => {
  const categories = COMMUNITY_CATEGORIES.filter(cat => cat.type === type);

  const categoryMap = new Map<string, string[]>();
  for (const cat of categories) {
    categoryMap.set(cat.name, cat.keywords);
  }

  const aggregatedData: { [key: string]: any } = {};

  for (const item of tableData) {
    let categoryName = item.description;

    for (const [name, keywords] of categoryMap.entries()) {
      if (keywords.some(keyword =>
        item.description.toLowerCase().includes(keyword.toLowerCase())
      )) {
        categoryName = name;
        break;
      }
    }

    if (!aggregatedData[categoryName]) {
      aggregatedData[categoryName] = {
        ...item,
        description: categoryName,
        monthlyData: { ...item.monthlyData },
        sum: item.sum,
      };
    } else {
      for (const month in item.monthlyData) {
        aggregatedData[categoryName].monthlyData[month] =
          (aggregatedData[categoryName].monthlyData[month] || 0) +
          item.monthlyData[month];
      }
      aggregatedData[categoryName].sum += item.sum;
    }
  }

  const total = Object.values(aggregatedData).reduce(
    (acc, item) => acc + item.sum,
    0
  );

  // ✅ sort by amount (sum), descending
  return Object.values(aggregatedData)
    .map(item => ({
      ...item,
      percentage: total > 0 ? (item.sum / total) * 100 : 0,
    }))
    .sort((a, b) => b.sum - a.sum);
};


export async function getCategorizedAnnualReport(accountId: string, startDate: Date, endDate: Date) {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  const rawReport = await getAccountDetails(accountId, startDate, endDate);

  if (!rawReport.success || !rawReport.data) {
    return rawReport;
  }

  const categorizedIncomes = await categorizeAndAggregate(
    rawReport.data.transactions.incomes.tableData,
    userId,
    'CREDIT'
  );

  const categorizedExpenses = await categorizeAndAggregate(
    rawReport.data.transactions.expenses.tableData,
    userId,
    'DEBIT'
  );

  const newIncomeTotal = categorizedIncomes.reduce((sum, item) => sum + item.sum, 0);
  const newExpenseTotal = categorizedExpenses.reduce((sum, item) => sum + item.sum, 0);

  const newIncomeMonthlySums = rawReport.data.transactions.dateRange.reduce((acc, month) => {
    acc[month] = categorizedIncomes.reduce((sum, item) => sum + (item.monthlyData[month] || 0), 0);
    return acc;
  }, {} as { [key: string]: number });

  const newExpenseMonthlySums = rawReport.data.transactions.dateRange.reduce((acc, month) => {
    acc[month] = categorizedExpenses.reduce((sum, item) => sum + (item.monthlyData[month] || 0), 0);
    return acc;
  }, {} as { [key: string]: number });

  console.log("categorizedIncomes", categorizedIncomes)
  console.log("categorizedExpenses", categorizedExpenses)

  return {
    success: true,
    data: {
      ...rawReport.data,
      transactions: {
        ...rawReport.data.transactions,
        incomes: {
          tableData: categorizedIncomes,
          total: newIncomeTotal,
          monthlySums: newIncomeMonthlySums,
        },
        expenses: {
          tableData: categorizedExpenses,
          total: newExpenseTotal,
          monthlySums: newExpenseMonthlySums,
        },
      },
    },
  };
}
