import React, { useMemo } from "react";
import {
  Square,
  SquareCheckBig,
} from "lucide-react";
import {
  type PaymentAccount,
  type Payment,
} from "@/hooks/use-payment-requests";



export const generateRemittanceSlip = (payment: Payment, defaultRemitterAccount: PaymentAccount | undefined) => {

  const remitter = payment.remitterAccount || defaultRemitterAccount;
  const payee = payment.payeeAccount;

  // 🟢 Case 1: 存款憑證 (Deposit Slip)
  if (payment.paymentMethod === "存款") {
    return (
      <div
        key={payment.id}
        className="border-2 border-brown-500 px-2 mb-3 bg-white font-sans"
      >
        <h2 className="text-center text-lg print:text-2xl font-bold mb-1 border-b py-2">
          <span
            className="text-lg font-sans"
            style={{ letterSpacing: "0.3em" }}
          >
            {payment.month}月
          </span>
          {payment.accountingSubject}{" "}
          <span className="print:text-xl">(存款憑證)</span>
        </h2>

        <div className="grid grid-cols-12 gap-1 text-sm print:text-lg">
          {/* 存入帳號 */}
          <div className="col-span-2 font-bold bg-red-50 pl-2 p-1">
            存入帳號
          </div>
          <div className="col-span-4 border p-1 text-xl font-mono font-bold tracking-[0.4em]">
            {payee?.accountNumber || "未指定"}
          </div>
          {/* 戶名 (受款人名稱) */}
          <div className="col-span-2 font-bold bg-red-50 pl-2 p-1">戶名</div>
          <div className="col-span-4 border text-xl font-bold p-1">
            {payee?.accountName || "未指定"}
          </div>

          {/* 存款指示 */}
          <div className="col-span-2 font-bold bg-red-50 pl-2 p-1">
            存款指示
          </div>
          <div className="col-span-10 border p-1 flex gap-4">
            <div className="flex flex-rows items-center gap-2">
              <SquareCheckBig /> 現金
            </div>
            <div className="flex flex-rows items-center gap-2">
              <Square /> 轉帳
            </div>
            <div className="flex flex-rows items-center gap-2">
              <Square /> 交換票據
            </div>
            <div className="ml-4">張數：＿＿</div>
          </div>

          {/* 存款人資訊 */}
          <div className="col-span-2 font-bold bg-red-50 pl-2 p-1">
            存款人
          </div>
          <div className="col-span-10 border p-1">
            統一證號：<span className="font-bold">********　</span>
            姓名：
            <span className="font-bold">
              {remitter?.accountName || "未指定"}　
            </span>
            電話：<span className="font-bold">{`2779-1052`}</span>
          </div>

          {/* 備註 */}
          <div className="col-span-2 font-bold bg-red-50 pl-2 p-1">備註</div>
          <div className="col-span-4 border text-xl font-bold p-1">
            {payment.remarks || "存款"}
          </div>

          {/* 存款金額 */}
          <div className="col-span-2 font-bold bg-red-50 pl-2 p-1">
            存款金額
          </div>
          <div className="col-span-4 border px-2 py-[0.1rem] text-right text-2xl font-serif font-bold">
            {payment.amount.toLocaleString()} 元
          </div>
        </div>
      </div>
    );
  }

  // 🟢 Case 2: No payee account → Cash withdrawal slip
  if (remitter && payment.paymentMethod === "領現") {
    if (!payee && !remitter) {
      return (
        <div key={payment.id} className="text-red-500 p-4">
          無法產生取款憑證: 付款 {payment.sequenceId}{" "}
          未指定匯款人帳戶，且無預設匯款帳戶。
        </div>
      );
    }

    return (
      <div
        key={payment.id}
        className="border-2 border-sky-500 px-2 mb-3 bg-white font-sans"
      >
        <h2 className="text-center text-lg print:text-2xl font-bold mb-1 border-b py-2">
          <span
            className="text-lg font-sans"
            style={{ letterSpacing: "0.3em" }}
          >
            {payment.month}月
          </span>
          {payment.remarks}{" "}
          <span className="print:text-xl">(取款憑證)</span>
        </h2>

        <div className="grid grid-cols-12 gap-1 text-sm print:text-lg">
          {/* 帳號 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">
            取款帳號
          </div>
          <div className="col-span-10 border p-1 text-xl font-mono font-bold tracking-[0.5em]">
            {remitter.accountNumber}
          </div>

          {/* 支付方式 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">
            付款指示
          </div>
          <div className="col-span-10 border p-1">
            <div className="flex flex-rows items-center gap-1">
              <SquareCheckBig /> 現金　　NT$
              <span className="font-bold ml-2">
                {payment.amount.toLocaleString()}
              </span>
            </div>
          </div>

          {/* 備註 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">備註</div>
          <div className="col-span-5 border text-lg whitespace-pre-wrap font-bold p-1">
            {payment.accountingSubject || "現金提款"}
          </div>

          {/* 金額 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">金額</div>
          <div className="col-span-3 border text-right text-2xl font-serif font-bold px-2 py-[0.1rem]">
            {payment.amount.toLocaleString()} 元
          </div>
        </div>
      </div>
    );
  }

  // 🟢 Case 3: Same bank transfer slip
  if (payee && payee.bankCode === remitter?.bankCode) {
    return (
      <div
        key={payment.id}
        className="border-2 border-sky-500 px-2 mb-3 bg-white font-sans"
      >
        <h2 className="text-center text-lg print:text-2xl font-bold mb-1 border-b py-2">
          <span
            className="text-lg font-sans"
            style={{ letterSpacing: "0.3em" }}
          >
            {payment.month}月
          </span>
          {payment.accountingSubject}{" "}
          <span className="print:text-xl">(取款憑證)</span>
        </h2>

        <div className="grid grid-cols-12 gap-1 text-sm print:text-lg">
          {/* 帳號 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">
            取款帳號
          </div>
          <div className="col-span-10 border p-1 text-xl font-mono font-bold tracking-[0.5em]">
            {remitter.accountNumber}
          </div>

          {/* 支付方式 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">
            付款指示
          </div>
          <div className="col-span-10 border p-1">
            <div className="flex flex-rows items-center gap-1">
              <SquareCheckBig /> 轉帳／其他　NT$
              <span className="font-bold ml-2">
                {payment.amount.toLocaleString()}
              </span>
            </div>
          </div>

          {/* 收款人帳號 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">
            收款人帳號
          </div>
          <div className="col-span-10 border text-xl font-mono font-bold tracking-[0.3em] p-1">
            {payee.accountNumber}
          </div>

          {/* 收款人戶名 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">
            收款人戶名
          </div>
          <div className="col-span-10 border text-xl p-1">
            {payee.accountName}
          </div>

          {/* 備註 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">備註</div>
          <div className="col-span-4 border text-xl font-bold p-1">
            {payment.remarks || "本行轉帳"}
          </div>

          {/* 金額 */}
          <div className="col-span-2 font-bold bg-blue-50 pl-2 p-1">金額</div>
          <div className="col-span-4 border text-right text-2xl font-serif font-bold px-2 py-[0.1rem]">
            {payment.amount.toLocaleString()} 元
          </div>
        </div>
      </div>
    );
  }

  // 🟢 Case 4: Normal bank transfer slip
  if (!remitter) {
    return (
      <div key={payment.id} className="text-red-500 p-4">
        無法產生匯款單: 付款 {payment.sequenceId}{" "}
        未指定匯款人帳戶，且無預設匯款帳戶。
      </div>
    );
  }

  return (
    <div
      key={payment.id}
      className="border-2 border-green-500 px-2 mb-3 bg-white font-sans"
    >
      <h2 className="text-center text-lg print:text-2xl font-bold mb-1 border-b py-2">
        <span
          className="text-lg font-sans"
          style={{ letterSpacing: "0.3em" }}
        >
          {payment.month}月
        </span>
        {payment.accountingSubject}{" "}
        <span className="print:text-xl">(匯出匯款憑證)</span>
      </h2>
      <div className="grid grid-cols-12 gap-1 text-sm print:text-lg">
        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">
          匯入行庫
        </div>
        <div className="col-span-4 border p-1 text-xl font-semibold">
          {payee?.bankName} <span className="text-sm font-normal">銀行</span>　
          {payee?.branchName} <span className="text-sm font-normal">分行</span>
        </div>
        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">
          匯款帳號
        </div>
        <div
          className="col-span-4 border p-1 text-xl font-sans font-bold"
          style={{ letterSpacing: "0.3em" }}
        >
          {remitter.accountNumber}
        </div>

        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">
          收款人帳號
        </div>
        <div
          className="col-span-10 border p-1 text-xl font-sans font-bold"
          style={{ letterSpacing: "0.3em" }}
        >
          {payee?.accountNumber}
        </div>

        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">
          收款人戶名
        </div>
        <div className="col-span-4 border p-1 text-xl">
          {payee?.accountName}
        </div>
        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">匯費</div>
        <div className="col-span-4 border p-1 pr-2 text-right text-xl font-serif font-bold">
          {payee?.bankCode != remitter.bankCode ? 30 : "同銀行轉帳免手續費"}{" "}
          <span className="text-sm font-normal">元</span>
        </div>

        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">
          匯款人
        </div>
        <div className="col-span-4 border p-1 text-xl">
          {remitter.accountName}
        </div>
        <div className="col-span-2 bg-green-50 px-2 p-1 font-bold">
          統一編號
        </div>
        <div className="col-span-4 flex items-center justify-between border p-1 text-lg font-sans">
          ********　<span className="text-base font-semibold">電話：</span>
          <span className="text-base font-serif font-bold pr-2">
            2279-1052
          </span>
        </div>

        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">備註</div>
        <div className="col-span-4 border p-1 text-xl font-sans font-bold">
          {payment.remarks}
        </div>
        <div className="col-span-2 bg-green-50 pl-2 p-1 font-bold">
          匯款金額
        </div>
        <div className="col-span-4 border py-[0.1rem] px-2 text-right text-xl font-serif font-bold">
          <span className="text-2xl">
            {payee?.bankCode === remitter.bankCode
              ? `${payment.amount.toLocaleString()} 元`
              : `${(payment.amount - 30).toLocaleString()} 元`}
          </span>
        </div>
      </div>
    </div>
  );
};