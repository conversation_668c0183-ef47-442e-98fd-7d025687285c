import React, { useMemo, useState, Fragment } from "react";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";

// --- Type Definitions ---
type PettyCashData = {
  id: string;
  accountName: string;
  totalIncome: number;
  totalExpense: number;
  previousBalance: number;
  currentBalance: number;
  cumulativeBalance: number;
};

type PettyCashSummaryProps = {
  accountName: string;
  summary: {
    totalIncome: number;
    totalExpense: number;
    previousBalance: number;
    currentBalance: number;
    cumulativeBalance: number;
  };
};

const columnHelper = createColumnHelper<PettyCashData>();

// --- Reusable Cell Component for Number Formatting ---
const FormattedNumberCell = ({
  value,
  className,
  isBold = false,
  applyRedForNegative = true,
}: {
  value: number | string;
  className?: string;
  isBold?: boolean;
  applyRedForNegative?: boolean;
}) => {
  const numericValue = Number(value) || 0;
  const isNegative = numericValue < 0;

  const formattedValue = new Intl.NumberFormat("zh-TW", {
    style: "currency",
    currency: "TWD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numericValue);

  return (
    <TableCell className={cn("py-[0.1rem] text-right border-t border-b-2 border-black", className)}>
      <span
        className={cn({
          "text-red-500": isNegative && applyRedForNegative,
          "font-bold": isBold,
        })}
      >
        {formattedValue}
      </span>
    </TableCell>
  );
};

// --- Main Component ---
export const PettyCashSummary = ({ accountName, summary }: PettyCashSummaryProps) => {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [headerHidden, setHeaderHidden] = useState<boolean>(false);

  const processedData = useMemo(
    () => [
      {
        id: "petty-cash-row",
        accountName,
        totalIncome: summary.totalIncome,
        totalExpense: summary.totalExpense,
        previousBalance: summary.previousBalance,
        currentBalance: summary.currentBalance,
        cumulativeBalance: summary.cumulativeBalance,
      },
    ],
    [accountName, summary]
  );

  const columns = useMemo(
    () => [
      columnHelper.accessor("accountName", {
        header: "帳戶名稱",
        cell: (info) => (
          <TableCell className="py-[0.1rem] pl-1 text-left font-medium border-t border-b-2 border-black">
            {info.getValue()}
          </TableCell>
        ),
      }),
      columnHelper.accessor("previousBalance", {
        header: "上期累積結餘",
        cell: (info) => (
          <FormattedNumberCell
            value={info.getValue()}
            className="px-1 py-[0.1rem]"
            applyRedForNegative={false} // Typically not styled red
          />
        ),
      }),
      columnHelper.accessor("totalIncome", {
        header: "本期新增",
        cell: (info) => (
          <FormattedNumberCell value={info.getValue()} className="px-1 py-[0.1rem]" />
        ),
      }),
      columnHelper.accessor("totalExpense", {
        header: "本期支出",
        cell: (info) => (
          <FormattedNumberCell value={info.getValue()} className="px-1 py-[0.1rem]" />
        ),
      }),
      columnHelper.accessor("currentBalance", {
        header: "本期結餘",
        cell: (info) => (
          <FormattedNumberCell value={info.getValue()} className="px-1 py-[0.1rem]" />
        ),
      }),
      columnHelper.accessor("cumulativeBalance", {
        header: "本期累積結餘",
        cell: (info) => (
          <FormattedNumberCell
            value={info.getValue()}
            className="pr-2 py-[0.1rem]"
            isBold
          />
        ),
      }),
    ],
    []
  );

  const table = useReactTable({
    data: processedData,
    columns,
    state: {
      columnVisibility,
    },
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const toggleHeaderVisibility = () => {
    setHeaderHidden((prev) => !prev);
  };

  return (
    <div className="space-y-1">
      <div id="no-print" className="flex gap-2">
        <h4 className="font-bold px-8 pt-3">{accountName}管理帳戶</h4>
        <div className="flex grow justify-end pt-2 gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="h-8">
                Columns
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table.getAllLeafColumns().map((column) => (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={(value) => column.toggleVisibility(!!value)}
                >
                  {column.id}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            variant="outline"
            className="h-8"
            type="button"
            onClick={toggleHeaderVisibility}
          >
            {headerHidden ? "Show Header" : "Hide Header"}
          </Button>
        </div>
      </div>

      <div className="space-y-2 border-none">
        <Table className="text-[18px]">
          <TableHeader
            style={{ display: headerHidden ? "none" : "table-header-group" }}
          >
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="border-t border-b border-black">
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className={cn(
                      "h-8 border-t-2 border-b border-black bg-green-100 px-1 py-0 text-black",
                      {
                        "w-[20rem] pl-1 text-left":
                          header.id === "accountName",
                        "w-[16rem] text-right":
                          header.id === "previousBalance" ||
                          header.id === "totalIncome" ||
                          header.id === "totalExpense" ||
                          header.id === "currentBalance", 
                        "w-[16rem] pr-2 text-right":
                          header.id === "cumulativeBalance",
                      }
                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                    {header.column.getIsSorted() && (
                      <span>
                        {header.column.getIsSorted() === "asc" ? " ↑" : " ↓"}
                      </span>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="bg-white text-black">
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id} className="border-t border-b border-black">
                {row.getVisibleCells().map((cell) => (
                  <Fragment key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Fragment>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};