"use client";

import { useEffect, useState, useRef } from "react";
import { BlockNoteEditor as BlockNoteEditorType } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { useCreateBlockNote } from "@blocknote/react";
import { BlockNoteView } from "@blocknote/shadcn";
import { toast } from "sonner";

interface BlockNoteEditorProps {
  initialContent?: string;
  onContentChange: (html: string) => void;
}

export default function BlockNoteEditor({ initialContent, onContentChange }: BlockNoteEditorProps) {
  const [isReady, setIsReady] = useState(false);
  const editorRef = useRef<BlockNoteEditorType | null>(null);
  const changeHandlerRef = useRef<(() => void) | null>(null);

  // Create editor instance
  useEffect(() => {
    let mounted = true;

    const init = async () => {
      try {
        if (!editorRef.current && mounted) {
          const editor = useCreateBlockNote({});
          editorRef.current = editor;

          // Set up change handler
          if (!changeHandlerRef.current) {
            const handler = editor.onChange(async () => {
              try {
                const html = await editor.blocksToHTMLLossy(editor.topLevelBlocks);
                onContentChange(html);
              } catch (error) {
                console.error('Failed to convert blocks to HTML:', error);
                toast.error('Failed to save changes');
              }
            });
            changeHandlerRef.current = handler || (() => {});
          }

          // If there's initial content, parse and set it
          if (initialContent) {
            const blocks = await editor.tryParseHTMLToBlocks(initialContent);
            editor.replaceBlocks(editor.topLevelBlocks, blocks);
          }

          if (mounted) {
            setIsReady(true);
          }
        }
      } catch (error) {
        console.error('Failed to initialize editor:', error);
        if (mounted) {
          toast.error('Failed to initialize editor');
        }
      }
    };

    init();

    // Cleanup
    return () => {
      mounted = false;
      if (changeHandlerRef.current) {
        changeHandlerRef.current();
        changeHandlerRef.current = null;
      }
      if (editorRef.current) {
        editorRef.current = null;
      }
      setIsReady(false);
    };
  }, [initialContent, onContentChange]);

  if (!isReady || !editorRef.current) {
    return (
      <div className="border rounded-lg min-h-[400px] flex items-center justify-center">
        Loading editor...
      </div>
    );
  }

  return (
    <div className="border rounded-lg min-h-[400px] overflow-hidden">
      <BlockNoteView 
        editor={editorRef.current}
        theme="light"
      />
    </div>
  );
}
