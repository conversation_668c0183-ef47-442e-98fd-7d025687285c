
"use server";
import { prisma } from "@/lib/db";
import { Prisma } from "@/lib/types";
import { startOfMonth, endOfMonth, subMonths } from "date-fns";

export async function getPettyCashSummary(accountId: string, date: Date) {
  try {
    const reportingMonthStart = startOfMonth(date);
    const reportingMonthEnd = endOfMonth(date);
    const previousMonthEnd = endOfMonth(subMonths(date, 1));

    // 1. Get Previous Balance
    /*const previousBalanceRecord = await prisma.balance.findFirst({
      where: {
        accountId: accountId,
        date: { lte: previousMonthEnd },
      },
      orderBy: { date: 'desc' },
    });
    const previousBalance = previousBalanceRecord?.amount ?? 0;
    */

    const previousIncome = await prisma.income.aggregate({
      _sum: { amount: true },
      where: {
        accountId: accountId,
        date: {
          lte: previousMonthEnd,
        },
      },
    });

    const previousExpense = await prisma.expense.aggregate({
      _sum: { amount: true },
      where: {
        accountId: accountId,
        date: {
          lte: previousMonthEnd,
        },
      },
    });
    const previousBalance = new (Prisma as any).Decimal(
        previousIncome._sum.amount ?? 0
      ).minus(new (Prisma as any).Decimal(previousExpense._sum.amount ?? 0)).toNumber();

    // 2. Get Current Period's Income
    const currentIncome = await prisma.income.aggregate({
      _sum: { amount: true },
      where: {
        accountId: accountId,
        date: {
          gte: reportingMonthStart,
          lte: reportingMonthEnd,
        },
      },
    });
    const totalIncome = currentIncome._sum.amount ?? 0;
    console.log("totalIncome", totalIncome);


    // 3. Get Current Period's Expense
    const currentExpense = await prisma.expense.aggregate({
      _sum: { amount: true },
      where: {
        accountId: accountId,
        date: {
          gte: reportingMonthStart,
          lte: reportingMonthEnd,
        },
      },
    });
    const totalExpense = currentExpense._sum.amount ?? 0;
    console.log("totalExpense", totalExpense);
    
    const currentBalance = new (Prisma as any).Decimal(totalIncome).minus(new (Prisma as any).Decimal(totalExpense)).toNumber();
    console.log("currentBalance", currentBalance);
    console.log("previousBalance", previousBalance);
    console.log("cumulativeBalance", previousBalance + currentBalance);

    return {
      success: true,
      data: {
        previousBalance: previousBalance,
        totalIncome: Number(totalIncome),
        totalExpense: Number(totalExpense),
        currentBalance: currentBalance,
        cumulativeBalance: previousBalance + currentBalance,
      },
    };
  } catch (error) {
    console.error("Error fetching petty cash summary:", error);
    return { success: false, error: "Failed to fetch summary." };
  }
}
